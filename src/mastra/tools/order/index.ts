/**
 * MOOLY.VN - ORDER TOOLS
 *
 * Tậ<PERSON> hợp các tools quản lý đơn hàng đã được tối ưu hóa:
 * - Tạo đơn hàng với tối ưu hóa tự động
 * - <PERSON>ra cứu thông tin đơn hàng
 * - Cập nhật trạng thái đơn hàng
 * - Hủy đơn hàng
 * - Theo dõi đơn hàng
 * - Quản lý khiếu nại
 */

import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import { getOrderByCode } from "../../../services/supabase/product.service";
import { getShippingFees, getShippingMethodDetails } from "../../../services/postgres/shipping.service";
import { DEFAULT_SHIPPING_CARRIER, generateRandomTrackingNumber } from "./default-values";

// ===== PRODUCTION TOOLS - CHỈ SỬ DỤNG CÁC TOOLS NÀY =====

// Tool tạo đơn hàng - DUY NHẤT cho hệ thống
export { createOrderTool } from "./create-order";

// Tool lấy thông tin đơn hàng theo mã
export { getOrderByCodeTool } from "./get-order";

// Tool cập nhật trạng thái đơn hàng
export { updateOrderStatusTool } from "./update-order-status";

// Tool hủy đơn hàng cho khách hàng
export { cancelOrderTool } from "./cancel-order";

// Tool demo hệ thống mã đơn hàng tối ưu
export { orderNumberDemoTool } from "./order-number-demo";

// ===== UTILITY TOOLS =====

// Công cụ lấy phí vận chuyển cố định
export const getShippingFeesTool = createTool({
  id: "get_shipping_fees",
  description: "Lấy phí vận chuyển cố định từ bảng shipping_fees",
  inputSchema: z.object({}),
  execute: async ({ runtimeContext }) => {

    try {
      const tenant_id = runtimeContext.get("tenant_id");

      if (!tenant_id) {
        return {
          success: false,
          error: "Thiếu thông tin tenant_id trong runtime context",
        };
      }

      const result = await getShippingFees({
        tenant_id: tenant_id.toString(),
      });

      if (!result.success || !result.data) {
        return {
          success: true,
          content: "Không tìm thấy phí vận chuyển",
        };
      }

      return {
        success: true,
        shippings: result.data,
      };
    } catch (error: any) {
      console.error("Lỗi khi lấy phí vận chuyển:", error);
      return {
        success: false,
        error: `Lỗi khi lấy phí vận chuyển: ${error?.message || "Lỗi không xác định"}`,
      };
    }
  },
});

// Công cụ theo dõi trạng thái đơn hàng
export const trackOrderTool = createTool({
  id: "track_order",
  description: "Theo dõi trạng thái đơn hàng chi tiết",
  inputSchema: z.object({
    order_code: z.string().describe("Mã đơn hàng"),
  }),
  execute: async ({ context, runtimeContext }) => {

    try {
      const tenant_id = runtimeContext.get("tenant_id");

      if (!tenant_id) {
        return {
          success: false,
          error: "Thiếu thông tin tenant_id trong runtime context",
        };
      }

      const result = await getOrderByCode({
        order_code: context.order_code,
        tenant_id: tenant_id.toString(),
      });

      if (!result.success) {
        return {
          success: false,
          error: result.message || "Không tìm thấy đơn hàng",
        };
      }

      const orderData = result.data;
      if (!orderData) {
        return {
          success: false,
          error: "Không thể lấy dữ liệu đơn hàng",
        };
      }

      const order = orderData as any;
      const trackingInfo: any = {
        order_id: order.id,
        order_number: order.order_number,
        status: order.status,
        created_at: order.created_at,
        updated_at: order.updated_at,
        customer_name: order.customer_name,
        customer_phone: order.customer_phone,
        payment_method: order.payment_method,
        shipping_method: order.shipping_method,
        subtotal: order.subtotal,
        shipping_fee: order.shipping_amount || order.shipping_fee || 0,
        total_amount: order.total_amount,
        items: Array.isArray(order.items) ? order.items : [],
        history: Array.isArray(order.history) ? order.history : [
          {
            status: "pending",
            created_at: order.created_at || new Date().toISOString(),
            comment: "Đơn hàng mới được tạo",
          },
        ],
        shipping_address: order.shipping_address || {},
      };

      // Thêm thông tin vận chuyển cho đơn hàng đang giao/đã giao
      if (order.status === "shipped" || order.status === "delivered") {
        trackingInfo.current_location = order.status === "delivered"
          ? order.shipping_address?.address || "Địa chỉ giao hàng"
          : "Đang vận chuyển";
        trackingInfo.shipping_carrier = DEFAULT_SHIPPING_CARRIER;
        trackingInfo.tracking_number = generateRandomTrackingNumber();

        // Lấy thông tin estimated_delivery từ shipping_method nếu có
        if (order.shipping_method) {
          try {
            const shippingResult = await getShippingMethodDetails({
              shipping_method_id: order.shipping_method,
              tenant_id: tenant_id.toString(),
            });

            if (shippingResult.success && shippingResult.data) {
              trackingInfo.estimated_delivery = shippingResult.data.estimated_delivery;
            } else {
              trackingInfo.estimated_delivery = "3-5 ngày làm việc";
            }
          } catch (error) {
            console.error("Lỗi khi lấy thông tin shipping method:", error);
            trackingInfo.estimated_delivery = "3-5 ngày làm việc";
          }
        } else {
          trackingInfo.estimated_delivery = "3-5 ngày làm việc";
        }
      }

      return {
        success: true,
        order_status: trackingInfo,
      };
    } catch (error: any) {
      console.error("Lỗi khi lấy thông tin đơn hàng:", error);
      return {
        success: false,
        error: `Lỗi khi lấy thông tin đơn hàng: ${error?.message || "Lỗi không xác định"}`,
      };
    }
  },
});

// Công cụ lấy thông tin về chính sách đổi trả
export const getReturnPolicyTool = createTool({
  id: "get_return_policy",
  description: "Lấy thông tin về chính sách đổi trả",
  inputSchema: z.object({}),
  execute: async () => {
    return {
      return_policy: {
        title: "Chính sách đổi trả của Mooly.vn",
        general_policy: "Mooly.vn cam kết đảm bảo sự hài lòng của khách hàng với tất cả sản phẩm.",
        return_period: "30 ngày kể từ ngày nhận hàng",
        conditions: [
          "Sản phẩm còn nguyên tem mác, chưa qua sử dụng hoặc giặt",
          "Có hóa đơn mua hàng hoặc mã đơn hàng",
          "Sản phẩm không bị hư hỏng do lỗi của người sử dụng",
        ],
        excluded_items: [
          "Đồ lót, đồ bơi vì lý do vệ sinh",
          "Sản phẩm đã được giặt hoặc sử dụng",
          "Sản phẩm bị hư hỏng do người dùng",
        ],
        return_process: [
          "Liên hệ với bộ phận chăm sóc khách hàng",
          "Cung cấp mã đơn hàng và lý do đổi trả",
          "Nhận mã đổi trả và hướng dẫn gửi hàng",
          "Gửi sản phẩm về địa chỉ được cung cấp",
          "Nhận sản phẩm mới hoặc hoàn tiền trong vòng 7 ngày làm việc",
        ],
        refund_methods: [
          "Hoàn tiền vào tài khoản ngân hàng",
          "Hoàn tiền vào ví điện tử",
          "Đổi sản phẩm mới",
        ],
        contact_info: {
          phone: "1900 1234",
          email: "<EMAIL>",
          working_hours: "8:00 - 20:00, từ Thứ 2 đến Chủ Nhật",
        },
      },
    };
  },
});

// Công cụ đăng ký khiếu nại
export const registerComplaintTool = createTool({
  id: "register_complaint",
  description: "Đăng ký khiếu nại về đơn hàng",
  inputSchema: z.object({
    order_id: z.string().describe("Mã đơn hàng"),
    issue_description: z.string().describe("Mô tả vấn đề"),
  }),
  execute: async ({ context, runtimeContext }) => {
    try {
      const tenant_id = runtimeContext.get("tenant_id");

      if (!tenant_id) {
        return {
          success: false,
          error: "Thiếu thông tin tenant_id trong runtime context",
        };
      }

      // Kiểm tra đơn hàng tồn tại
      const orderResult = await getOrderByCode({
        order_code: context.order_id,
        tenant_id: tenant_id.toString(),
      });

      if (!orderResult.success) {
        return {
          success: false,
          error: `Không tìm thấy đơn hàng với mã ${context.order_id}`,
        };
      }

      // Tạo mã khiếu nại
      const timestamp = Date.now();
      const randomChars = Math.random()
        .toString(36)
        .substring(2, 6)
        .toUpperCase();
      const complaintId = `KN-${timestamp}-${randomChars}`;

      return {
        success: true,
        complaint_id: complaintId,
        status: "Đã tiếp nhận",
        estimated_response_time: "24 giờ",
        message: "Khiếu nại của bạn đã được tiếp nhận. Chúng tôi sẽ phản hồi trong vòng 24 giờ.",
      };
    } catch (error: any) {
      console.error("Lỗi khi đăng ký khiếu nại:", error);
      return {
        success: false,
        error: `Lỗi khi đăng ký khiếu nại: ${error?.message || "Lỗi không xác định"}`,
      };
    }
  },
});