import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import { 
  generateOptimizedOrderNumber, 
  generateCustomOrderNumber,
  parseOrderNumber,
  getOrderCountByDate,
  OrderNumberConfig 
} from "../../../services/order-number.service";

/**
 * TOOL DEMO HỆ THỐNG MÃ ĐƠN HÀNG TỐI ƯU
 * 
 * Tool này giúp test và demo các tính năng của hệ thống mã đơn hàng mới:
 * - Tạo mã đơn hàng tối ưu (DH + DDMMYY + HHMM)
 * - Tạo mã đơn hàng với format tùy chỉnh
 * - Phân tích mã đơn hàng
 * - Thống kê đơn hàng theo ngày
 */
export const orderNumberDemoTool = createTool({
  id: "order_number_demo",
  description: "Demo và test hệ thống mã đơn hàng tối ưu - tạo mã ngắn gọn, d<PERSON> nhớ và unique",
  inputSchema: z.object({
    action: z
      .enum([
        "generate_optimized",
        "generate_custom", 
        "parse_order_number",
        "get_order_count",
        "demo_all"
      ])
      .describe("Hành động cần thực hiện"),
    
    // Cho generate_optimized
    prefix: z
      .string()
      .optional()
      .describe("Prefix cho mã đơn hàng (mặc định: DH)"),
    include_seconds: z
      .boolean()
      .optional()
      .describe("Có bao gồm giây không (mặc định: false)"),
    
    // Cho generate_custom
    custom_format: z
      .string()
      .optional()
      .describe("Format tùy chỉnh (ví dụ: ORDER-{DDMMYY}-{HHMM})"),
    
    // Cho parse_order_number
    order_number: z
      .string()
      .optional()
      .describe("Mã đơn hàng cần phân tích"),
    
    // Cho get_order_count
    date: z
      .string()
      .optional()
      .describe("Ngày cần thống kê (YYYY-MM-DD, mặc định: hôm nay)"),
  }),
  execute: async ({ context, runtimeContext }) => {
    try {
      // Lấy tenant_id từ runtime context
      const tenant_id = runtimeContext.get("tenant_id");
      if (!tenant_id) {
        return {
          success: false,
          error: "Thiếu thông tin tenant_id trong runtime context",
        };
      }

      const { action } = context;

      switch (action) {
        case "generate_optimized": {
          const config: OrderNumberConfig = {
            prefix: context.prefix || "DH",
            includeSeconds: context.include_seconds || false,
            timezone: "Asia/Ho_Chi_Minh"
          };

          const orderNumber = await generateOptimizedOrderNumber(tenant_id.toString(), config);
          
          return {
            success: true,
            data: {
              order_number: orderNumber,
              config: config,
              format_explanation: {
                prefix: config.prefix,
                date_format: "DDMMYY (ngày/tháng/năm 2 chữ số)",
                time_format: config.includeSeconds ? "HHMMSS" : "HHMM",
                suffix: "A, B, C... nếu trùng lặp",
                example: `${config.prefix}2507181455 = 25/07/2018 14:55`
              }
            },
            message: `✅ Đã tạo mã đơn hàng tối ưu: ${orderNumber}`
          };
        }

        case "generate_custom": {
          if (!context.custom_format) {
            return {
              success: false,
              error: "Vui lòng cung cấp custom_format"
            };
          }

          const orderNumber = await generateCustomOrderNumber(tenant_id.toString(), context.custom_format);
          
          return {
            success: true,
            data: {
              order_number: orderNumber,
              custom_format: context.custom_format,
              available_placeholders: {
                "{DD}": "Ngày (01-31)",
                "{MM}": "Tháng (01-12)", 
                "{YY}": "Năm 2 chữ số (18, 19, 20...)",
                "{YYYY}": "Năm 4 chữ số (2018, 2019...)",
                "{HH}": "Giờ (00-23)",
                "{mm}": "Phút (00-59)",
                "{SS}": "Giây (00-59)",
                "{DDMMYY}": "Ngày tháng năm liền (250718)",
                "{HHMM}": "Giờ phút liền (1455)",
                "{HHMMSS}": "Giờ phút giây liền (145530)"
              }
            },
            message: `✅ Đã tạo mã đơn hàng tùy chỉnh: ${orderNumber}`
          };
        }

        case "parse_order_number": {
          if (!context.order_number) {
            return {
              success: false,
              error: "Vui lòng cung cấp order_number cần phân tích"
            };
          }

          const config: OrderNumberConfig = {
            prefix: context.prefix || "DH",
            includeSeconds: context.include_seconds || false
          };

          const parsed = parseOrderNumber(context.order_number, config);
          
          if (!parsed) {
            return {
              success: false,
              error: `Không thể phân tích mã đơn hàng: ${context.order_number}`
            };
          }

          return {
            success: true,
            data: {
              order_number: context.order_number,
              parsed_info: {
                date: parsed.date.toISOString(),
                date_formatted: parsed.date.toLocaleString('vi-VN', { 
                  timeZone: 'Asia/Ho_Chi_Minh',
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit'
                }),
                prefix: parsed.prefix,
                suffix: parsed.suffix || "Không có",
                is_today: parsed.date.toDateString() === new Date().toDateString()
              }
            },
            message: `✅ Đã phân tích mã đơn hàng: ${context.order_number}`
          };
        }

        case "get_order_count": {
          const targetDate = context.date ? new Date(context.date) : new Date();
          const count = await getOrderCountByDate(tenant_id.toString(), targetDate);
          
          return {
            success: true,
            data: {
              date: targetDate.toISOString().split('T')[0],
              date_formatted: targetDate.toLocaleDateString('vi-VN'),
              order_count: count,
              is_today: targetDate.toDateString() === new Date().toDateString()
            },
            message: `📊 Số đơn hàng ngày ${targetDate.toLocaleDateString('vi-VN')}: ${count} đơn`
          };
        }

        case "demo_all": {
          // Demo tất cả tính năng
          const results = [];

          // 1. Tạo mã tối ưu cơ bản
          const optimizedBasic = await generateOptimizedOrderNumber(tenant_id.toString());
          results.push({
            type: "Mã tối ưu cơ bản",
            result: optimizedBasic,
            description: "Format: DH + DDMMYY + HHMM"
          });

          // 2. Tạo mã tối ưu với giây
          const optimizedWithSeconds = await generateOptimizedOrderNumber(tenant_id.toString(), {
            includeSeconds: true
          });
          results.push({
            type: "Mã tối ưu có giây",
            result: optimizedWithSeconds,
            description: "Format: DH + DDMMYY + HHMMSS"
          });

          // 3. Tạo mã với prefix tùy chỉnh
          const customPrefix = await generateOptimizedOrderNumber(tenant_id.toString(), {
            prefix: "ORDER"
          });
          results.push({
            type: "Mã với prefix tùy chỉnh",
            result: customPrefix,
            description: "Format: ORDER + DDMMYY + HHMM"
          });

          // 4. Tạo mã với format hoàn toàn tùy chỉnh
          const fullyCustom = await generateCustomOrderNumber(tenant_id.toString(), "HD-{DDMMYY}-{HHMM}");
          results.push({
            type: "Mã format tùy chỉnh",
            result: fullyCustom,
            description: "Format: HD-{DDMMYY}-{HHMM}"
          });

          // 5. Phân tích mã đầu tiên
          const parsed = parseOrderNumber(optimizedBasic);
          results.push({
            type: "Phân tích mã đơn hàng",
            result: parsed ? {
              date: parsed.date.toLocaleString('vi-VN', { timeZone: 'Asia/Ho_Chi_Minh' }),
              prefix: parsed.prefix,
              suffix: parsed.suffix || "Không có"
            } : "Không phân tích được",
            description: `Phân tích mã: ${optimizedBasic}`
          });

          // 6. Thống kê đơn hàng hôm nay
          const todayCount = await getOrderCountByDate(tenant_id.toString());
          results.push({
            type: "Thống kê hôm nay",
            result: `${todayCount} đơn hàng`,
            description: "Số đơn hàng được tạo trong ngày hôm nay"
          });

          return {
            success: true,
            data: {
              demo_results: results,
              summary: {
                total_demos: results.length,
                recommended_format: "DH + DDMMYY + HHMM",
                advantages: [
                  "Ngắn gọn: chỉ 10-11 ký tự",
                  "Dễ nhớ: có thể đọc được ngày giờ",
                  "Unique: đảm bảo không trùng lặp",
                  "Tối ưu: giảm độ dài so với timestamp"
                ]
              }
            },
            message: `🎯 Demo hoàn tất! Đã test ${results.length} tính năng của hệ thống mã đơn hàng tối ưu`
          };
        }

        default:
          return {
            success: false,
            error: "Hành động không hợp lệ"
          };
      }
    } catch (error: any) {
      console.error("❌ Lỗi trong order number demo:", error);
      return {
        success: false,
        error: `Lỗi: ${error?.message || "Lỗi không xác định"}`,
      };
    }
  },
});
