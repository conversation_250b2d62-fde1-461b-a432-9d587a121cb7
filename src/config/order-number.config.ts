/**
 * CẤU HÌNH HỆ THỐNG MÃ ĐƠN HÀNG TỐI ƯU
 * 
 * File này chứa các cấu hình mặc định và tùy chỉnh cho hệ thống mã đơn hàng.
 * C<PERSON> thể mở rộng để lưu cấu hình riêng cho từng tenant trong database.
 */

export interface OrderNumberConfig {
  prefix?: string;
  includeSeconds?: boolean;
  timezone?: string;
  customFormat?: string;
  maxSuffixAttempts?: number;
}

/**
 * Cấu hình mặc định cho hệ thống
 */
export const DEFAULT_ORDER_NUMBER_CONFIG: Required<OrderNumberConfig> = {
  prefix: 'DH',
  includeSeconds: false,
  timezone: 'Asia/Ho_Chi_Minh',
  customFormat: '', // Rỗng = sử dụng format mặc định
  maxSuffixAttempts: 26 // A-Z
};

/**
 * Cấu hình theo từng loại business
 */
export const BUSINESS_TYPE_CONFIGS: Record<string, OrderNumberConfig> = {
  // Bán lẻ thời trang
  retail: {
    prefix: 'DH',
    includeSeconds: false,
    timezone: 'Asia/Ho_Chi_Minh'
  },
  
  // Dịch vụ số
  digital: {
    prefix: 'DV',
    includeSeconds: true, // Cần độ chính xác cao hơn
    timezone: 'Asia/Ho_Chi_Minh'
  },
  
  // Dịch vụ
  services: {
    prefix: 'SV',
    includeSeconds: false,
    timezone: 'Asia/Ho_Chi_Minh'
  },
  
  // Hybrid
  hybrid: {
    prefix: 'HB',
    includeSeconds: false,
    timezone: 'Asia/Ho_Chi_Minh'
  }
};

/**
 * Các format mẫu có thể sử dụng
 */
export const SAMPLE_FORMATS = {
  // Format cơ bản (khuyến nghị)
  basic: 'DH{DDMMYY}{HHMM}',
  
  // Format có dấu phân cách
  separated: 'DH-{DDMMYY}-{HHMM}',
  
  // Format đầy đủ
  full: 'ORDER{YYYY}{MM}{DD}{HHMMSS}',
  
  // Format ngắn
  short: '{DDMMYY}{HHMM}',
  
  // Format có slash
  slash: 'DH{DD}/{MM}/{YY}-{HHMM}',
  
  // Format theo giờ Việt Nam
  vietnam: 'HD{DDMMYY}{HHMM}',
  
  // Format cho hóa đơn
  invoice: 'HD{YYYY}{MM}{DD}{HHMM}',
  
  // Format cho đơn hàng online
  online: 'ON{DDMMYY}{HHMM}',
  
  // Format cho đơn hàng offline
  offline: 'OF{DDMMYY}{HHMM}'
};

/**
 * Prefix theo ngành hàng
 */
export const INDUSTRY_PREFIXES = {
  fashion: 'TT',      // Thời trang
  food: 'TA',         // Thực ăn
  electronics: 'DT',  // Điện tử
  books: 'SA',        // Sách
  cosmetics: 'MP',    // Mỹ phẩm
  jewelry: 'TT',      // Trang sức
  sports: 'TT',       // Thể thao
  home: 'NT',         // Nội thất
  health: 'SK',       // Sức khỏe
  education: 'GD',    // Giáo dục
  travel: 'DL',       // Du lịch
  automotive: 'OT',   // Ô tô
  default: 'DH'       // Đơn hàng
};

/**
 * Cấu hình theo múi giờ
 */
export const TIMEZONE_CONFIGS = {
  vietnam: 'Asia/Ho_Chi_Minh',
  singapore: 'Asia/Singapore',
  thailand: 'Asia/Bangkok',
  malaysia: 'Asia/Kuala_Lumpur',
  indonesia: 'Asia/Jakarta',
  philippines: 'Asia/Manila',
  utc: 'UTC'
};

/**
 * Lấy cấu hình cho tenant cụ thể
 * Trong tương lai có thể lấy từ database
 */
export function getOrderNumberConfigForTenant(
  tenantId: string,
  businessType?: string,
  industry?: string
): OrderNumberConfig {
  // Cấu hình mặc định
  let config = { ...DEFAULT_ORDER_NUMBER_CONFIG };
  
  // Áp dụng cấu hình theo business type
  if (businessType && BUSINESS_TYPE_CONFIGS[businessType]) {
    config = { ...config, ...BUSINESS_TYPE_CONFIGS[businessType] };
  }
  
  // Áp dụng prefix theo ngành hàng
  if (industry && INDUSTRY_PREFIXES[industry]) {
    config.prefix = INDUSTRY_PREFIXES[industry];
  }
  
  // TODO: Trong tương lai có thể lấy cấu hình riêng từ database
  // const customConfig = await getCustomConfigFromDatabase(tenantId);
  // config = { ...config, ...customConfig };
  
  return config;
}

/**
 * Validate format string
 */
export function validateOrderNumberFormat(format: string): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  // Kiểm tra các placeholder hợp lệ
  const validPlaceholders = [
    '{DD}', '{MM}', '{YY}', '{YYYY}', '{HH}', '{mm}', '{SS}',
    '{DDMMYY}', '{HHMM}', '{HHMMSS}'
  ];
  
  // Tìm tất cả placeholder trong format
  const placeholders = format.match(/\{[^}]+\}/g) || [];
  
  for (const placeholder of placeholders) {
    if (!validPlaceholders.includes(placeholder)) {
      errors.push(`Placeholder không hợp lệ: ${placeholder}`);
    }
  }
  
  // Kiểm tra độ dài dự kiến
  let estimatedLength = format.length;
  for (const placeholder of validPlaceholders) {
    const count = (format.match(new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g')) || []).length;
    if (count > 0) {
      const placeholderLength = placeholder.length;
      let replacementLength = 2; // Mặc định 2 ký tự
      
      if (placeholder === '{YYYY}') replacementLength = 4;
      else if (placeholder === '{DDMMYY}') replacementLength = 6;
      else if (placeholder === '{HHMM}') replacementLength = 4;
      else if (placeholder === '{HHMMSS}') replacementLength = 6;
      
      estimatedLength += (replacementLength - placeholderLength) * count;
    }
  }
  
  // Cảnh báo nếu quá dài
  if (estimatedLength > 20) {
    errors.push(`Format có thể tạo mã quá dài (dự kiến: ${estimatedLength} ký tự)`);
  }
  
  // Cảnh báo nếu quá ngắn
  if (estimatedLength < 8) {
    errors.push(`Format có thể tạo mã quá ngắn (dự kiến: ${estimatedLength} ký tự)`);
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Tạo format gợi ý dựa trên yêu cầu
 */
export function suggestOrderNumberFormat(requirements: {
  includeDate?: boolean;
  includeTime?: boolean;
  includeSeconds?: boolean;
  prefix?: string;
  separator?: string;
  maxLength?: number;
}): string[] {
  const suggestions: string[] = [];
  const { includeDate = true, includeTime = true, includeSeconds = false, prefix = 'DH', separator = '', maxLength = 15 } = requirements;
  
  let datePart = '';
  let timePart = '';
  
  if (includeDate) {
    datePart = '{DDMMYY}';
  }
  
  if (includeTime) {
    timePart = includeSeconds ? '{HHMMSS}' : '{HHMM}';
  }
  
  // Tạo các gợi ý
  if (datePart && timePart) {
    suggestions.push(`${prefix}${datePart}${timePart}`);
    if (separator) {
      suggestions.push(`${prefix}${separator}${datePart}${separator}${timePart}`);
    }
  } else if (datePart) {
    suggestions.push(`${prefix}${datePart}`);
  } else if (timePart) {
    suggestions.push(`${prefix}${timePart}`);
  }
  
  // Lọc theo độ dài tối đa
  return suggestions.filter(format => {
    const validation = validateOrderNumberFormat(format);
    return validation.isValid && format.length <= maxLength;
  });
}

/**
 * Export các constant để sử dụng
 */
export {
  SAMPLE_FORMATS as ORDER_NUMBER_SAMPLE_FORMATS,
  INDUSTRY_PREFIXES as ORDER_NUMBER_INDUSTRY_PREFIXES,
  TIMEZONE_CONFIGS as ORDER_NUMBER_TIMEZONE_CONFIGS
};
