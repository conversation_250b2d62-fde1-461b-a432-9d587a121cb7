import * as pgProductService from '../postgres/product.service';
import { updateOrder } from '../postgres/order.service';
import * as pgPromotionService from '../postgres/promotion.service';
import { supabaseAdmin } from '../../config/supabase';
import { generateOptimizedOrderNumber } from '../order-number.service';

// Đã chuyển hàm filterProductsByAttributes sang file postgres/product.service.ts

/**
 * Tìm kiếm sản phẩm theo SKU
 * @param sku Mã SKU của sản phẩm
 * @param tenant_id ID của tenant
 */
/**
 * Lấy thông tin chi tiết của nhiều sản phẩm theo danh sách ID
 * Tối ưu hóa để lấy thông tin cần thiết: tên, giá, biến thể, tồn kho, mô tả
 * @param productIds Danh sách ID sản phẩm cần lấy thông tin
 * @param tenant_id ID của tenant
 * @param bot_id ID của bot (để lọc sản phẩm theo quyền truy cập)
 */
export const getProductDetailsByIds = async ({
  productIds,
  tenant_id,
  bot_id,
}: {
  productIds: string[];
  tenant_id: string;
  bot_id?: string;
}) => {
  // Sử dụng kết nối PostgreSQL trực tiếp thay vì Supabase
  return pgProductService.getProductDetailsByIds({
    productIds,
    tenant_id,
    bot_id
  });
};

export const getProductBySku = async ({
  sku,
  tenant_id,
}: {
  sku: string;
  tenant_id: string;
}) => {
  // Sử dụng kết nối PostgreSQL trực tiếp thay vì Supabase
  return pgProductService.getProductBySku({
    sku,
    tenant_id
  });
};

/**
 * Lấy danh sách sản phẩm từ PostgreSQL với các bộ lọc
 * @param filters Các bộ lọc để tìm kiếm sản phẩm
 * @param tenant_id ID của tenant
 * @param limit Số lượng sản phẩm tối đa trả về
 * @param offset Vị trí bắt đầu lấy dữ liệu
 */
export const getProducts = async ({
  filters = {},
  tenant_id,
  limit = 10,
  offset = 0,
}: {
  filters?: {
    query?: string;
    category_id?: string;
    category?: string;
    color?: string;
    size?: string;
    style?: string;
    price_range?: { min?: number; max?: number };
    is_active?: boolean;
  };
  tenant_id: string;
  limit?: number;
  offset?: number;
}) => {
  // Sử dụng kết nối PostgreSQL trực tiếp thay vì Supabase
  return pgProductService.getProducts({
    filters,
    tenant_id,
    limit,
    offset
  });
};

/**
 * Lấy thông tin chi tiết của một sản phẩm
 * @param product_id ID của sản phẩm
 * @param sku Mã SKU của sản phẩm
 * @param tenant_id ID của tenant
 */
export const getProductDetails = async ({
  product_id,
  sku,
  tenant_id,
}: {
  product_id: string;
  sku: string;
  tenant_id: string;
}) => {
  // Sử dụng kết nối PostgreSQL trực tiếp thay vì Supabase
  return pgProductService.getProductDetails({
    product_id,
    sku,
    tenant_id
  });
};

/**
 * Kiểm tra tình trạng còn hàng của sản phẩm
 * @param product_id ID của sản phẩm
 * @param variant_id ID của biến thể (nếu có)
 * @param tenant_id ID của tenant
 */
export const checkProductAvailability = async ({
  product_id,
  variant_id,
  tenant_id,
}: {
  product_id: string;
  variant_id?: string;
  tenant_id: string;
}) => {
  // Sử dụng kết nối PostgreSQL trực tiếp thay vì Supabase
  return pgProductService.checkProductAvailability({
    product_id,
    variant_id,
    tenant_id
  });
};

/**
 * Tạo đơn hàng mới sử dụng Supabase Admin
 * @param orderData Thông tin đơn hàng
 * @param tenant_id ID của tenant
 */
export const createOrder = async ({
  orderData,
  tenant_id,
}: {
  orderData: {
    customer_id?: string;
    customer_info: {
      name: string;
      phone: string;
      email?: string | null;
    };
    shipping_address: {
      full_name: string;
      phone: string;
      address: string;
      province?: string;
      district?: string;
      ward?: string;
      country?: string;
    };
    items: Array<{
      product_id: string;
      variant_id?: string;
      quantity: number;
      unit_price: number;
    }>;
    payment_method: string;
    shipping_method?: string;
    shipping_fee?: number;
    promotion_code?: string;
    notes?: string;
    chatbot_info?: {
      resource_id?: string;
      thread_id?: string;
      bot_id?: string;
      created_at?: string;
    };
  };
  tenant_id: string;
}) => {
  // Sử dụng Supabase Admin để tạo đơn hàng với quyền service role

  try {

    // Tạo order number tối ưu theo format #DH + DDMMYY + HHMM
    const orderNumber = await generateOptimizedOrderNumber(tenant_id);

    // Tính tổng giá trị đơn hàng
    const subtotal = orderData.items.reduce((sum, item) => sum + (item.quantity * item.unit_price), 0);
    const shippingAmount = orderData.shipping_fee || 0;

    // Tính discount từ promotion nếu có
    let discountAmount = 0;
    if (orderData.promotion_code) {
      try {
        // Lấy thông tin promotion
        const { data: promotion, error: promotionError } = await supabaseAdmin
          .from('promotions')
          .select('*')
          .eq('code', orderData.promotion_code)
          .eq('tenant_id', tenant_id)
          .eq('is_active', true)
          .single();

        if (!promotionError && promotion) {
          // Kiểm tra điều kiện áp dụng
          const now = new Date();
          const startDate = promotion.start_date ? new Date(promotion.start_date) : null;
          const endDate = promotion.end_date ? new Date(promotion.end_date) : null;

          const isValidTime = (!startDate || now >= startDate) && (!endDate || now <= endDate);
          const isValidAmount = !promotion.min_purchase_amount || subtotal >= promotion.min_purchase_amount;

          if (isValidTime && isValidAmount) {
            if (promotion.value_type === 'percentage') {
              discountAmount = (subtotal * promotion.value) / 100;
              if (promotion.max_discount_amount && discountAmount > promotion.max_discount_amount) {
                discountAmount = promotion.max_discount_amount;
              }
            } else if (promotion.value_type === 'fixed') {
              discountAmount = promotion.value;
            }
          }
        }
      } catch (error) {
      }
    }

    const totalAmount = subtotal + shippingAmount - discountAmount;

    // Tạo đơn hàng
    const { data: order, error: orderError } = await supabaseAdmin
      .from('orders')
      .insert({
        tenant_id,
        customer_id: orderData.customer_id || null,
        customer_name: orderData.customer_info.name,
        customer_phone: orderData.customer_info.phone,
        customer_email: orderData.customer_info.email || null,
        payment_method: orderData.payment_method,
        shipping_method: orderData.shipping_method || null,
        notes: orderData.notes || null,
        status: 'pending',
        order_number: orderNumber,
        subtotal,
        shipping_amount: shippingAmount,
        discount_amount: discountAmount,
        total_amount: totalAmount,
        chatbot_info: orderData.chatbot_info || null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (orderError) {
      throw new Error(`Không thể tạo đơn hàng: ${orderError.message}`);
    }

    // Tạo địa chỉ giao hàng
    const { data: address, error: addressError } = await supabaseAdmin
      .from('customer_addresses')
      .insert({
        tenant_id,
        customer_id: orderData.customer_id || null,
        full_name: orderData.shipping_address.full_name,
        phone: orderData.shipping_address.phone,
        address: orderData.shipping_address.address,
        province: orderData.shipping_address.province || null,
        district: orderData.shipping_address.district || null,
        ward: orderData.shipping_address.ward || null,
        country: orderData.shipping_address.country || 'Vietnam',
        address_type: 'shipping',
        is_default_shipping: false,
        is_default_billing: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (addressError) {
      console.error('Lỗi khi tạo địa chỉ giao hàng:', addressError);
      // Không throw error vì đơn hàng đã được tạo thành công
    } else if (address) {
      // Cập nhật đơn hàng với shipping_address_id
      await supabaseAdmin
        .from('orders')
        .update({
          shipping_address_id: address.id,
          billing_address_id: address.id, // Sử dụng cùng địa chỉ cho billing
          updated_at: new Date().toISOString()
        })
        .eq('id', order.id);
    }

    // Tạo các order items
    const orderItems = [];
    for (const item of orderData.items) {
      // Lấy thông tin sản phẩm
      const { data: product, error: productError } = await supabaseAdmin
        .from('products')
        .select('name, sku, avatar')
        .eq('id', item.product_id)
        .eq('tenant_id', tenant_id)
        .single();

      if (productError) {
        console.error(`Lỗi khi lấy thông tin sản phẩm ${item.product_id}:`, productError);
        continue;
      }

      let variantInfo = null;
      if (item.variant_id) {
        const { data: variant, error: variantError } = await supabaseAdmin
          .from('product_variants')
          .select('name, sku, attributes, avatar')
          .eq('id', item.variant_id)
          .eq('tenant_id', tenant_id)
          .single();

        if (!variantError && variant) {
          variantInfo = {
            name: variant.name,
            sku: variant.sku,
            attributes: variant.attributes
          };
        }
      }

      const { data: orderItem, error: itemError } = await supabaseAdmin
        .from('order_items')
        .insert({
          tenant_id,
          order_id: order.id,
          product_id: item.product_id,
          variant_id: item.variant_id || null,
          name: variantInfo?.name || product.name,
          sku: variantInfo?.sku || product.sku,
          quantity: item.quantity,
          unit_price: item.unit_price,
          total_price: item.quantity * item.unit_price,
          discount_amount: 0,
          tax_amount: 0,
          image_url: product.avatar || null,
          variant_info: variantInfo,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (itemError) {
        console.error(`❌ Lỗi khi tạo order item cho sản phẩm ${item.product_id}:`, itemError);
        // Throw error để dừng quá trình tạo đơn hàng nếu không thể tạo order item
        throw new Error(`Không thể tạo order item cho sản phẩm ${item.product_id}: ${itemError.message}`);
      } else {
        orderItems.push(orderItem);
      }
    }

    // Tạo lịch sử đơn hàng
    await supabaseAdmin
      .from('order_history')
      .insert({
        tenant_id,
        order_id: order.id,
        status: 'pending',
        previous_status: null,
        comment: 'Đơn hàng được tạo mới',
        user_id: null,
        created_at: new Date().toISOString()
      });

    return {
      success: true,
      data: {
        order_id: order.id,
        order_number: order.order_number,
        status: order.status,
        subtotal: order.subtotal,
        shipping_amount: order.shipping_amount,
        discount_amount: order.discount_amount,
        total_amount: order.total_amount,
        items: orderItems,
        shipping_address: address,
        ...order
      },
      message: 'Tạo đơn hàng thành công',
    };
  } catch (error: any) {
    console.error('Lỗi khi tạo đơn hàng:', error);
    return {
      success: false,
      message: `Lỗi khi tạo đơn hàng: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};

/**
 * Lấy đầy đủ hình ảnh của sản phẩm theo ID
 * @param product_id ID của sản phẩm
 * @param tenant_id ID của tenant
 * @param maxImages Số lượng hình ảnh tối đa (mặc định 10)
 */
export const getProductImages = async ({
  product_id,
  tenant_id,
  maxImages = 10,
}: {
  product_id: string;
  tenant_id: string;
  maxImages?: number;
}) => {
  // Sử dụng kết nối PostgreSQL trực tiếp thay vì Supabase
  return pgProductService.getProductImages({
    product_id,
    tenant_id,
    maxImages
  });
};

/**
 * Lấy danh sách danh mục sản phẩm
 * @param tenant_id ID của tenant
 */
export const getProductCategories = async ({ tenant_id }: { tenant_id: string }) => {
  // Sử dụng kết nối PostgreSQL trực tiếp thay vì Supabase
  return pgProductService.getProductCategories({
    tenant_id
  });
};

/**
 * Lấy thông tin đơn hàng theo order_code (order_number)
 * @param order_code Mã đơn hàng
 * @param tenant_id ID của tenant
 */
export const getOrderByCode = async ({
  order_code,
  tenant_id,
}: {
  order_code: string;
  tenant_id: string;
}) => {
  // Sử dụng kết nối PostgreSQL trực tiếp thay vì Supabase
  return pgProductService.getOrderByCode({
    order_code,
    tenant_id
  });
};

/**
 * Cập nhật trạng thái đơn hàng
 * @param order_id ID của đơn hàng
 * @param status Trạng thái mới của đơn hàng (sử dụng enum order_status_enum)
 * @param comment Ghi chú về việc thay đổi trạng thái
 * @param tenant_id ID của tenant
 */
export const updateOrderStatus = async ({
  order_id,
  status,
  comment,
  tenant_id,
}: {
  order_id: string;
  status: string; // Giá trị enum: 'pending', 'processing', 'shipped', 'delivered', 'cancelled'
  comment: string;
  tenant_id: string;
}) => {
  // Sử dụng kết nối PostgreSQL trực tiếp thay vì Supabase
  return pgProductService.updateOrderStatus({
    order_id,
    status,
    comment,
    tenant_id
  });
};

/**
 * Cập nhật thông tin đơn hàng
 * Cho phép cập nhật các thông tin khác nhau tùy thuộc vào trạng thái đơn hàng
 * @param order_id ID của đơn hàng
 * @param updateData Dữ liệu cập nhật
 * @param tenant_id ID của tenant
 */
export const updateOrderInfo = async ({
  order_id,
  updateData,
  tenant_id,
}: {
  order_id: string;
  updateData: {
    // Thông tin khách hàng
    customer_name?: string;
    customer_phone?: string;
    customer_email?: string;

    // Địa chỉ giao hàng
    shipping_address?: {
      full_name?: string;
      phone?: string;
      address?: string;
      province?: string;
      district?: string;
      ward?: string;
      country?: string;
    };

    // Thông tin đơn hàng
    notes?: string;
    shipping_method?: string;
    payment_method?: string;

    // Cập nhật sản phẩm trong đơn hàng
    items_update?: Array<{
      item_id: string; // ID của order_item cần cập nhật
      quantity?: number; // Số lượng mới
      action: 'update' | 'remove'; // Hành động: cập nhật hoặc xóa
    }>;

    // Thêm sản phẩm mới vào đơn hàng
    new_items?: Array<{
      product_id: string;
      variant_id?: string;
      quantity: number;
      unit_price: number;
    }>;
  };
  tenant_id: string;
}) => {
  // Sử dụng kết nối PostgreSQL trực tiếp thay vì Supabase
  return updateOrder({
    order_id,
    updateData,
    tenant_id
  });
};

/**
 * Lấy danh sách khuyến mãi đang hoạt động
 * @param tenant_id ID của tenant
 * @param promotion_code Mã khuyến mãi cụ thể (nếu có)
 * @param product_id ID sản phẩm để lọc khuyến mãi áp dụng cho sản phẩm cụ thể (nếu có)
 * @param category_id ID danh mục để lọc khuyến mãi áp dụng cho danh mục cụ thể (nếu có)
 */
export const getActivePromotions = async ({
  tenant_id,
  promotion_code,
  product_id,
  category_id,
}: {
  tenant_id: string;
  promotion_code?: string;
  product_id?: string;
  category_id?: string;
}) => {
  // Sử dụng kết nối PostgreSQL trực tiếp thay vì Supabase
  return pgPromotionService.getActivePromotions({
    tenant_id,
    promotion_code,
    product_id,
    category_id
  });
};
