import { supabaseAdmin } from '../config/supabase';
import {
  getOrderNumberConfigForTenant,
  DEFAULT_ORDER_NUMBER_CONFIG,
  type OrderNumberConfig
} from '../config/order-number.config';

/**
 * SERVICE QUẢN LÝ MÃ ĐƠN HÀNG TỐI ƯU
 * 
 * Tạo mã đơn hàng ngắn gọn, dễ nhớ và unique theo format:
 * DH + DDMMYY + HHMM + [Suffix]
 * 
 * Ví dụ:
 * - DH2507181455 (25/07/2018 14:55)
 * - DH2507181455A (nếu trùng thì thêm suffix A, B, C...)
 * 
 * Ưu điểm:
 * - Ngắn gọn: chỉ 10-11 ký tự
 * - Dễ nhớ: có thể đọc được ngày giờ
 * - Unique: đảm bảo không trùng lặp
 * - Tối ưu: giảm độ dài so với timestamp dài
 */

// Interface đã được move sang config file
export type { OrderNumberConfig } from '../config/order-number.config';

/**
 * Tạo mã đơn hàng tối ưu
 * @param tenant_id ID của tenant
 * @param config Cấu hình tùy chọn (sẽ merge với cấu hình mặc định của tenant)
 * @returns Mã đơn hàng unique
 */
export async function generateOptimizedOrderNumber(
  tenant_id: string,
  config: OrderNumberConfig = {}
): Promise<string> {
  // Lấy cấu hình mặc định cho tenant (có thể mở rộng để lấy từ database)
  const tenantConfig = getOrderNumberConfigForTenant(tenant_id);

  // Merge cấu hình
  const finalConfig = { ...tenantConfig, ...config };

  const {
    prefix = DEFAULT_ORDER_NUMBER_CONFIG.prefix,
    includeSeconds = DEFAULT_ORDER_NUMBER_CONFIG.includeSeconds,
    timezone = DEFAULT_ORDER_NUMBER_CONFIG.timezone
  } = finalConfig;

  // Tạo đối tượng Date với timezone Việt Nam
  const now = new Date();
  const vietnamTime = new Date(now.toLocaleString("en-US", { timeZone: timezone }));
  
  // Format: DDMMYY
  const day = String(vietnamTime.getDate()).padStart(2, '0');
  const month = String(vietnamTime.getMonth() + 1).padStart(2, '0');
  const year = String(vietnamTime.getFullYear()).slice(-2); // 2 chữ số cuối
  
  // Format: HHMM hoặc HHMMSS
  const hour = String(vietnamTime.getHours()).padStart(2, '0');
  const minute = String(vietnamTime.getMinutes()).padStart(2, '0');
  const second = includeSeconds ? String(vietnamTime.getSeconds()).padStart(2, '0') : '';
  
  // Tạo mã cơ bản
  const baseOrderNumber = `${prefix}${day}${month}${year}${hour}${minute}${second}`;
  
  // Kiểm tra xem mã đã tồn tại chưa
  const { data: existingOrder } = await supabaseAdmin
    .from('orders')
    .select('order_number')
    .eq('tenant_id', tenant_id)
    .eq('order_number', baseOrderNumber)
    .single();
  
  // Nếu chưa tồn tại, trả về mã cơ bản
  if (!existingOrder) {
    return baseOrderNumber;
  }
  
  // Nếu đã tồn tại, thêm suffix A, B, C...
  const suffixes = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  for (let i = 0; i < suffixes.length; i++) {
    const orderNumberWithSuffix = `${baseOrderNumber}${suffixes[i]}`;
    
    const { data: existingOrderWithSuffix } = await supabaseAdmin
      .from('orders')
      .select('order_number')
      .eq('tenant_id', tenant_id)
      .eq('order_number', orderNumberWithSuffix)
      .single();
    
    if (!existingOrderWithSuffix) {
      return orderNumberWithSuffix;
    }
  }
  
  // Fallback nếu tất cả suffix đã hết (rất hiếm khi xảy ra)
  // Thêm 2 ký tự ngẫu nhiên
  const randomSuffix = Math.random().toString(36).substr(2, 2).toUpperCase();
  return `${baseOrderNumber}${randomSuffix}`;
}

/**
 * Phân tích mã đơn hàng để lấy thông tin ngày giờ
 * @param orderNumber Mã đơn hàng
 * @param config Cấu hình đã dùng khi tạo mã
 * @returns Thông tin ngày giờ hoặc null nếu không phân tích được
 */
export function parseOrderNumber(
  orderNumber: string,
  config: OrderNumberConfig = {}
): {
  date: Date;
  prefix: string;
  suffix?: string;
} | null {
  const { prefix = 'DH', includeSeconds = false } = config;
  
  if (!orderNumber.startsWith(prefix)) {
    return null;
  }
  
  // Loại bỏ prefix
  const withoutPrefix = orderNumber.slice(prefix.length);
  
  // Tách phần ngày giờ và suffix
  const expectedLength = includeSeconds ? 10 : 8; // DDMMYYHHMMSS hoặc DDMMYYHHMM
  const dateTimeStr = withoutPrefix.slice(0, expectedLength);
  const suffix = withoutPrefix.slice(expectedLength) || undefined;
  
  if (dateTimeStr.length !== expectedLength) {
    return null;
  }
  
  try {
    // Parse ngày giờ
    const day = parseInt(dateTimeStr.slice(0, 2));
    const month = parseInt(dateTimeStr.slice(2, 4));
    const year = 2000 + parseInt(dateTimeStr.slice(4, 6)); // Chuyển YY thành YYYY
    const hour = parseInt(dateTimeStr.slice(6, 8));
    const minute = parseInt(dateTimeStr.slice(8, 10));
    const second = includeSeconds ? parseInt(dateTimeStr.slice(10, 12)) : 0;
    
    const date = new Date(year, month - 1, day, hour, minute, second);
    
    // Kiểm tra tính hợp lệ của ngày
    if (
      date.getDate() !== day ||
      date.getMonth() !== month - 1 ||
      date.getFullYear() !== year
    ) {
      return null;
    }
    
    return {
      date,
      prefix,
      suffix
    };
  } catch (error) {
    return null;
  }
}

/**
 * Tạo mã đơn hàng với format tùy chỉnh
 * @param tenant_id ID của tenant
 * @param customFormat Format tùy chỉnh (ví dụ: "ORDER-{DDMMYY}-{HHMM}")
 * @returns Mã đơn hàng unique
 */
export async function generateCustomOrderNumber(
  tenant_id: string,
  customFormat: string
): Promise<string> {
  const now = new Date();
  const vietnamTime = new Date(now.toLocaleString("en-US", { timeZone: "Asia/Ho_Chi_Minh" }));
  
  // Các placeholder có thể thay thế
  const replacements = {
    '{DD}': String(vietnamTime.getDate()).padStart(2, '0'),
    '{MM}': String(vietnamTime.getMonth() + 1).padStart(2, '0'),
    '{YY}': String(vietnamTime.getFullYear()).slice(-2),
    '{YYYY}': String(vietnamTime.getFullYear()),
    '{HH}': String(vietnamTime.getHours()).padStart(2, '0'),
    '{mm}': String(vietnamTime.getMinutes()).padStart(2, '0'),
    '{SS}': String(vietnamTime.getSeconds()).padStart(2, '0'),
    '{DDMMYY}': `${String(vietnamTime.getDate()).padStart(2, '0')}${String(vietnamTime.getMonth() + 1).padStart(2, '0')}${String(vietnamTime.getFullYear()).slice(-2)}`,
    '{HHMM}': `${String(vietnamTime.getHours()).padStart(2, '0')}${String(vietnamTime.getMinutes()).padStart(2, '0')}`,
    '{HHMMSS}': `${String(vietnamTime.getHours()).padStart(2, '0')}${String(vietnamTime.getMinutes()).padStart(2, '0')}${String(vietnamTime.getSeconds()).padStart(2, '0')}`
  };
  
  // Thay thế các placeholder
  let orderNumber = customFormat;
  for (const [placeholder, value] of Object.entries(replacements)) {
    orderNumber = orderNumber.replace(new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g'), value);
  }
  
  // Kiểm tra unique và thêm suffix nếu cần
  return await ensureUniqueOrderNumber(tenant_id, orderNumber);
}

/**
 * Đảm bảo mã đơn hàng là unique bằng cách thêm suffix
 * @param tenant_id ID của tenant
 * @param baseOrderNumber Mã đơn hàng cơ bản
 * @returns Mã đơn hàng unique
 */
async function ensureUniqueOrderNumber(
  tenant_id: string,
  baseOrderNumber: string
): Promise<string> {
  // Kiểm tra mã cơ bản
  const { data: existingOrder } = await supabaseAdmin
    .from('orders')
    .select('order_number')
    .eq('tenant_id', tenant_id)
    .eq('order_number', baseOrderNumber)
    .single();
  
  if (!existingOrder) {
    return baseOrderNumber;
  }
  
  // Thêm suffix A, B, C...
  const suffixes = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  for (let i = 0; i < suffixes.length; i++) {
    const orderNumberWithSuffix = `${baseOrderNumber}${suffixes[i]}`;
    
    const { data: existingOrderWithSuffix } = await supabaseAdmin
      .from('orders')
      .select('order_number')
      .eq('tenant_id', tenant_id)
      .eq('order_number', orderNumberWithSuffix)
      .single();
    
    if (!existingOrderWithSuffix) {
      return orderNumberWithSuffix;
    }
  }
  
  // Fallback với số ngẫu nhiên
  const randomSuffix = Math.random().toString(36).substr(2, 2).toUpperCase();
  return `${baseOrderNumber}${randomSuffix}`;
}

/**
 * Lấy thống kê mã đơn hàng theo ngày
 * @param tenant_id ID của tenant
 * @param date Ngày cần thống kê (mặc định: hôm nay)
 * @returns Số lượng đơn hàng trong ngày
 */
export async function getOrderCountByDate(
  tenant_id: string,
  date: Date = new Date()
): Promise<number> {
  const startOfDay = new Date(date);
  startOfDay.setHours(0, 0, 0, 0);
  
  const endOfDay = new Date(date);
  endOfDay.setHours(23, 59, 59, 999);
  
  const { count } = await supabaseAdmin
    .from('orders')
    .select('*', { count: 'exact', head: true })
    .eq('tenant_id', tenant_id)
    .gte('created_at', startOfDay.toISOString())
    .lte('created_at', endOfDay.toISOString());
  
  return count || 0;
}
